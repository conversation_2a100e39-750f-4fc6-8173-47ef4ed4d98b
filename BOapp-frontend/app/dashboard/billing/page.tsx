/*
This server page provides billing and subscription management with tiered pricing options.
*/

"use server"

import { PricingSection } from "@/components/pricing/pricing-section"
import { auth } from "@clerk/nextjs/server"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { getSubscriptionAction } from "@/actions/subscription-actions"

export default async function BillingPage() {
  const { userId } = await auth()

  // Fetch the user's current subscription data
  const subscriptionData = await getSubscriptionAction()

  // Determine the current plan based on subscription data
  let currentPlan = null
  let trialDaysLeft = 0

  if (subscriptionData.tier === "trial") {
    currentPlan = "trial"
    trialDaysLeft = subscriptionData.trialDaysLeft || 0
  } else if (
    subscriptionData.tier === "pro" &&
    subscriptionData.subscriptionType
  ) {
    currentPlan = subscriptionData.subscriptionType
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Billing & Subscription</h1>

      {/* Pricing Section */}
      <div className="rounded-lg border p-6">
        <h2 className="mb-6 text-xl font-semibold">Choose Your Plan</h2>
        <PricingSection
          userId={userId}
          showHeader={false}
          showAcademicAccess={false} // Hide Academic Access card in billing page
          showFooterNote={true}
          currentPlan={currentPlan}
          trialDaysLeft={trialDaysLeft}
        />
      </div>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-medium">Can I switch between plans?</h3>
            <p className="text-muted-foreground text-sm">
              Yes, you can upgrade or downgrade your plan at any time. Changes
              take effect at the start of your next billing cycle.
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">What happens when my trial ends?</h3>
            <p className="text-muted-foreground text-sm">
              When your 14-day trial ends, you'll need to choose a paid plan to
              continue using SynSilicO™. Your data will be preserved for 30
              days if you don't subscribe.
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">Do you offer refunds?</h3>
            <p className="text-muted-foreground text-sm">
              We offer a 14-day money-back guarantee on all paid plans. If
              you're not satisfied, contact our support team for a full refund.
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">Can I customize my plan?</h3>
            <p className="text-muted-foreground text-sm">
              Custom plans are available for enterprise customers. Contact our
              sales team for more information.
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">What about academic users?</h3>
            <p className="text-muted-foreground text-sm">
              Academic users can apply for extended 90-day trial access. Visit
              the
              <a
                href="/academic-signup"
                className="mx-1 font-medium text-blue-600 hover:underline dark:text-blue-400"
              >
                academic signup page
              </a>
              and complete the verification process.
            </p>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild>
            <a href="/contact">Contact Support</a>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
