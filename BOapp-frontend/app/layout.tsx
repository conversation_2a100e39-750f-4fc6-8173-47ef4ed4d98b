/*
The root server layout for the app.
*/

import {
  createProfileAction,
  getProfileByUserIdAction
} from "@/actions/db/profiles-actions"
import { Toaster } from "@/components/ui/toaster"
import { CookieScript } from "@/components/utilities/cookie-script"
import { Providers } from "@/components/utilities/providers"
import { TailwindIndicator } from "@/components/utilities/tailwind-indicator"
import { cn } from "@/lib/utils"
import { ClerkProvider } from "@clerk/nextjs"
import { auth } from "@clerk/nextjs/server"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Optimizer™",
  description:
    "Complex optimization made simple through intelligent experimental design."
}

export default async function RootLayout({
  children
}: {
  children: React.ReactNode
}) {
  const { userId } = await auth()

  if (userId) {
    try {
      const profileRes = await getProfileByUserIdAction(userId)

      // Only create a profile if it truly doesn't exist (not on database errors)
      if (!profileRes.isSuccess && profileRes.message === "Profile not found") {
        console.log(`Creating new profile for user ${userId}`)
        await createProfileAction({ userId })
      }
    } catch (error) {
      // Log the error but don't create a profile on errors
      console.error("Error checking user profile:", error)
    }
  }

  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
        <body
          className={cn(
            "bg-background mx-auto min-h-screen w-full scroll-smooth antialiased",
            inter.className
          )}
        >
          <Providers
            attribute="class"
            defaultTheme="light"
            enableSystem={false}
            disableTransitionOnChange
          >
            {children}

            <TailwindIndicator />

            <Toaster />

            {/* CookieScript Cookie Consent Widget */}
            <CookieScript
              enableDebugMode={process.env.NODE_ENV === "development"}
            />
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  )
}
