// actions/db/optimizations-actions.ts
"use server"

import { db } from "@/db/db";
import {
  optimizationsTable,
  measurementsTable,
  InsertOptimization,
  SelectOptimization,
  InsertMeasurement,
  SelectMeasurement
} from "@/db/schema/optimizations-schema";
import { ActionState } from "@/types";
import { eq, desc, sql, count } from "drizzle-orm";

// Create a new optimization
export async function createOptimizationDBAction(
  optimization: InsertOptimization
): Promise<ActionState<SelectOptimization>> {
  try {
    console.log("Creating optimization in database:", optimization);

    // Validate required fields
    if (!optimization.userId) {
      throw new Error("User ID is required");
    }

    if (!optimization.name) {
      throw new Error("Name is required");
    }

    if (!optimization.optimizerId) {
      throw new Error("Optimizer ID is required");
    }

    // Insert into database
    const [newOptimization] = await db.insert(optimizationsTable)
      .values(optimization)
      .returning();

    console.log("Optimization created successfully:", newOptimization);

    return {
      isSuccess: true,
      message: "Optimization created successfully",
      data: newOptimization
    };
  } catch (error) {
    console.error("Error creating optimization in database:", error);
    return {
      isSuccess: false,
      message: `Failed to create optimization in database: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Get all optimizations for a user
export async function getOptimizationsAction(
  userId: string
): Promise<ActionState<SelectOptimization[]>> {
  try {
    const optimizations = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.userId, userId))
      .orderBy(desc(optimizationsTable.createdAt));

    return {
      isSuccess: true,
      message: "Optimizations retrieved successfully",
      data: optimizations
    };
  } catch (error) {
    console.error("Error getting optimizations:", error);
    return {
      isSuccess: false,
      message: "Failed to get optimizations"
    };
  }
}

export async function getOptimizationsActionTest(): Promise<ActionState<SelectOptimization[]>> {
  try {
    const optimizations = await db.select().from(optimizationsTable)
      .orderBy(desc(optimizationsTable.createdAt));

    return {
      isSuccess: true,
      message: "Optimizations retrieved successfully",
      data: optimizations
    };
  } catch (error) {
    console.error("Error getting optimizations:", error)
    return {
      isSuccess: false,
      message: "Failed to get optimizations"
    };
  }
}


// Get a single optimization by ID
export async function getOptimizationByIdAction(
  id: string
): Promise<ActionState<SelectOptimization>> {
  try {
    const [optimization] = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.id, id))
      .limit(1);

    if (!optimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization retrieved successfully",
      data: optimization
    };
  } catch (error) {
    console.error("Error getting optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to get optimization"
    };
  }
}

// Get an optimization by its optimizer ID
export async function getOptimizationByOptimizerIdAction(
  optimizerId: string
): Promise<ActionState<SelectOptimization>> {
  try {
    const [optimization] = await db.select().from(optimizationsTable)
      .where(eq(optimizationsTable.optimizerId, optimizerId))
      .limit(1);

    if (!optimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization retrieved successfully",
      data: optimization
    };
  } catch (error) {
    console.error("Error getting optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to get optimization"
    };
  }
}

// Update an optimization
export async function updateOptimizationAction(
  id: string,
  data: Partial<InsertOptimization>
): Promise<ActionState<SelectOptimization>> {
  try {
    const [updatedOptimization] = await db.update(optimizationsTable)
      .set(data)
      .where(eq(optimizationsTable.id, id))
      .returning();

    if (!updatedOptimization) {
      return {
        isSuccess: false,
        message: "Optimization not found"
      };
    }

    return {
      isSuccess: true,
      message: "Optimization updated successfully",
      data: updatedOptimization
    };
  } catch (error) {
    console.error("Error updating optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to update optimization"
    };
  }
}

// Delete an optimization
export async function deleteOptimizationAction(
  id: string
): Promise<ActionState<void>> {
  try {
    await db.delete(optimizationsTable)
      .where(eq(optimizationsTable.id, id));

    return {
      isSuccess: true,
      message: "Optimization deleted successfully",
      data: undefined
    };
  } catch (error) {
    console.error("Error deleting optimization:", error);
    return {
      isSuccess: false,
      message: "Failed to delete optimization"
    };
  }
}

// Create a new measurement
export async function createMeasurementAction(
  measurement: InsertMeasurement
): Promise<ActionState<SelectMeasurement>> {
  try {
    console.log("Creating measurement in database:", measurement);

    // Validate required fields
    if (!measurement.optimizationId) {
      throw new Error("Optimization ID is required");
    }

    if (!measurement.parameters) {
      throw new Error("Parameters are required");
    }

    if (!measurement.targetValue && !measurement.targetValues) {
      throw new Error("Target value is required");
    }

    // Insert into database
    const [newMeasurement] = await db.insert(measurementsTable)
      .values(measurement)
      .returning();

    console.log("Measurement created successfully:", newMeasurement);

    return {
      isSuccess: true,
      message: "Measurement created successfully",
      data: newMeasurement
    };
  } catch (error) {
    console.error("Error creating measurement:", error);
    return {
      isSuccess: false,
      message: `Failed to create measurement: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

// Get all measurements for an optimization
export async function getMeasurementsAction(
  optimizationId: string
): Promise<ActionState<SelectMeasurement[]>> {
  try {
    const measurements = await db.select().from(measurementsTable)
      .where(eq(measurementsTable.optimizationId, optimizationId))
      .orderBy(desc(measurementsTable.createdAt));

    return {
      isSuccess: true,
      message: "Measurements retrieved successfully",
      data: measurements
    };
  } catch (error) {
    console.error("Error getting measurements:", error);
    return {
      isSuccess: false,
      message: "Failed to get measurements"
    };
  }
}

// Get measurement counts for all optimizations
export async function getMeasurementCountsAction(userId?: string): Promise<ActionState<Record<string, number>>> {
  try {
    // Create the query based on whether userId is provided
    const results = userId
      ? await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            count: count(measurementsTable.id)
          })
          .from(measurementsTable)
          .innerJoin(
            optimizationsTable,
            eq(measurementsTable.optimizationId, optimizationsTable.id)
          )
          .where(eq(optimizationsTable.userId, userId))
          .groupBy(measurementsTable.optimizationId)
      : await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            count: count(measurementsTable.id)
          })
          .from(measurementsTable)
          .groupBy(measurementsTable.optimizationId);

    // Convert to a map of optimization ID -> count
    const countMap: Record<string, number> = {};
    results.forEach(result => {
      countMap[result.optimizationId] = Number(result.count);
    });

    return {
      isSuccess: true,
      message: "Measurement counts retrieved successfully",
      data: countMap
    };
  } catch (error) {
    console.error("Error getting measurement counts:", error);
    return {
      isSuccess: false,
      message: "Failed to get measurement counts"
    };
  }
}

// Get total number of measurements for a user
export async function getTotalMeasurementsCountAction(userId: string): Promise<ActionState<number>> {
  try {
    // Get the total count of measurements for the user
    const result = await db
      .select({
        count: count(measurementsTable.id)
      })
      .from(measurementsTable)
      .innerJoin(
        optimizationsTable,
        eq(measurementsTable.optimizationId, optimizationsTable.id)
      )
      .where(eq(optimizationsTable.userId, userId));

    // Extract the count from the result
    const totalCount = result.length > 0 ? Number(result[0].count) : 0;

    return {
      isSuccess: true,
      message: "Total measurements count retrieved successfully",
      data: totalCount
    };
  } catch (error) {
    console.error("Error getting total measurements count:", error);
    return {
      isSuccess: false,
      message: "Failed to get total measurements count",
      data: 0
    };
  }
}

// Get latest measurement for each optimization
export async function getLatestMeasurementsAction(userId?: string): Promise<ActionState<Record<string, SelectMeasurement>>> {
  try {
    // Get the max createdAt for each optimization based on whether userId is provided
    const latestTimestamps = userId
      ? await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            maxCreatedAt: sql<string>`MAX(${measurementsTable.createdAt})`
          })
          .from(measurementsTable)
          .innerJoin(
            optimizationsTable,
            eq(measurementsTable.optimizationId, optimizationsTable.id)
          )
          .where(eq(optimizationsTable.userId, userId))
          .groupBy(measurementsTable.optimizationId)
      : await db
          .select({
            optimizationId: measurementsTable.optimizationId,
            maxCreatedAt: sql<string>`MAX(${measurementsTable.createdAt})`
          })
          .from(measurementsTable)
          .groupBy(measurementsTable.optimizationId);

    // Then, for each optimization, we get the measurement with that timestamp
    const latestMeasurements: Record<string, SelectMeasurement> = {};

    // For each optimization with its latest timestamp
    for (const { optimizationId, maxCreatedAt } of latestTimestamps) {
      const [measurement] = await db
        .select()
        .from(measurementsTable)
        .where(
          sql`${measurementsTable.optimizationId} = ${optimizationId} AND
              ${measurementsTable.createdAt} = ${maxCreatedAt}`
        )
        .limit(1);

      if (measurement) {
        latestMeasurements[optimizationId] = measurement;
      }
    }

    return {
      isSuccess: true,
      message: "Latest measurements retrieved successfully",
      data: latestMeasurements
    };
  } catch (error) {
    console.error("Error getting latest measurements:", error);
    return {
      isSuccess: false,
      message: "Failed to get latest measurements"
    };
  }
}