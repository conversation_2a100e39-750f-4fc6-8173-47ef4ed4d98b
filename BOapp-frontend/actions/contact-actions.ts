"use server"

import { Resend } from "resend"
import { z } from "zod"
import { ActionState } from "@/types"

// The from address for all emails
const fromEmail = process.env.EMAIL_FROM || "<EMAIL>"

// Create a function to get or create the Resend client
function getResendClient() {
  // Check if we have an API key
  const resendApiKey = process.env.RESEND_API_KEY

  if (!resendApiKey) {
    console.warn(
      "RESEND_API_KEY is not set. Email functionality will be disabled."
    )
    // Return a mock client that logs instead of sending
    return {
      emails: {
        send: async (options: any) => {
          console.log("Email would be sent (RESEND_API_KEY not set):", options)
          return {
            id: "mock-email-id",
            message: "Email sending skipped - no API key"
          }
        }
      }
    }
  }

  // Return the real client if we have an API key
  return new Resend(resendApiKey)
}

// Define the contact form schema
const contactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  message: z.string().min(10, "Message must be at least 10 characters")
})

// Define the type for contact form data
export type ContactFormData = z.infer<typeof contactFormSchema>

/**
 * Send a contact form email
 */
export async function sendContactFormEmail(
  data: ContactFormData
): Promise<ActionState<{ emailId: string }>> {
  console.log("POST /contact:", data);

  try {
    // Validate the data
    const validatedData = contactFormSchema.parse(data)
    const { name, email, message } = validatedData

    // Get the Resend client (real or mock)
    const resend = getResendClient()

    // Send the email
    const result = await resend.emails.send({
      from: fromEmail,
      to: ["<EMAIL>"], // The recipient email address
      subject: `Contact Form Submission from ${name}`,
      reply_to: email,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4f46e5;">New Contact Form Submission</h2>
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Message:</strong></p>
          <div style="background-color: #f9fafb; padding: 16px; border-radius: 4px; margin: 16px 0;">
            ${message.replace(/\n/g, "<br />")}
          </div>
          <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;" />
          <p style="color: #6b7280; font-size: 14px;">© ${new Date().getFullYear()} SynSilicO™. All rights reserved.</p>
        </div>
      `
    });

    // Handle both the mock client response and the actual Resend response
    const emailId = typeof result === 'object' && result !== null && 'id' in result
      ? result.id
      : "mock-email-id"

    return {
      isSuccess: true,
      data: { emailId },
      message: "Your message has been sent successfully!"
    }
  } catch (error) {
    console.error("Error sending contact form email:", error);
    return {
      isSuccess: false,
      message: error instanceof Error ? error.message : "Failed to send email"
    }
  }
}
