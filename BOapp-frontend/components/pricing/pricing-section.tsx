"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Check, Sparkles, GraduationCap, BookOpen, School } from "lucide-react"
import { motion } from "framer-motion"

interface PricingCardProps {
  title: string
  price: string
  description: string
  buttonText: string
  buttonLink: string
  features: string[]
  userId: string | null
  popular: boolean
  timeLimit: string
  isCurrentPlan?: boolean
}

function PricingCard({
  title,
  price,
  description,
  buttonText,
  buttonLink,
  features,
  userId,
  popular,
  timeLimit,
  isCurrentPlan = false
}: PricingCardProps) {
  // Handle different scenarios based on plan type and user authentication status
  let finalButtonLink = buttonLink
  let finalButtonText = buttonText

  if (title === "Trial") {
    // For trial, direct users to signup with redirect to welcome page
    // This ensures they complete the survey before accessing the dashboard
    finalButtonLink = "/signup?redirect_url=/welcome"
  } else if (!userId) {
    // For unauthenticated users wanting premium plans:
    // Direct to signup with plan selection and redirect to checkout after signup
    const planType = title === "Premium Monthly" ? "monthly" : "yearly"
    finalButtonLink = `/signup?redirect_url=/api/stripe/checkout&plan=${planType}`
    finalButtonText = "Sign Up & Subscribe"
  } else {
    // For authenticated users, direct them to our checkout API
    const planType = title === "Premium Monthly" ? "monthly" : "yearly"
    finalButtonLink = `/api/stripe/checkout?plan=${planType}`

    // Fallback to direct Stripe payment link if needed
    if (buttonLink && buttonLink.includes("buy.stripe.com")) {
      finalButtonLink = `${buttonLink}?client_reference_id=${userId}`
    }
  }

  return (
    <motion.div
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.3,
        delay: title === "Trial" ? 0 : title === "Premium Monthly" ? 0.1 : 0.2
      }}
    >
      <Card
        className={cn(
          "relative flex h-[380px] flex-col border-2 transition-all duration-200",
          isCurrentPlan
            ? "border-green-500 shadow-lg shadow-green-100 dark:border-green-600 dark:shadow-green-900/20"
            : popular
              ? "border-primary shadow-lg"
              : "hover:border-blue-300 dark:hover:border-blue-700"
        )}
      >
        {/* Current Plan Badge */}
        {isCurrentPlan && (
          <motion.div
            className="absolute -top-4 left-1/2 -translate-x-1/2 rounded-full bg-green-500 px-3 py-1 text-sm font-medium text-white"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.2 }}
          >
            <span className="flex items-center gap-1">
              <Check className="size-3" />
              Current Plan
            </span>
          </motion.div>
        )}

        {/* Popular Badge - only show if not current plan */}
        {popular && !isCurrentPlan && (
          <motion.div
            className="bg-primary text-primary-foreground absolute -top-4 left-1/2 -translate-x-1/2 rounded-full px-3 py-1 text-sm font-medium"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.2 }}
          >
            <span className="flex items-center gap-1">
              <Sparkles className="size-3" />
              Most Popular
            </span>
          </motion.div>
        )}

        <CardHeader className="pb-4">
          <CardTitle className="text-2xl">{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>

        <CardContent className="flex min-h-[180px] grow flex-col">
          <motion.div
            className="mb-4 flex items-baseline justify-center gap-x-2"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <span className="text-5xl font-bold">{price}</span>
            {price !== "Free" && (
              <span className="text-muted-foreground">/{timeLimit}</span>
            )}
          </motion.div>

          <ul className="mb-2 space-y-2">
            {features.map((feature, index) => (
              <motion.li
                key={index}
                className="flex items-center gap-x-2"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 * index, duration: 0.2 }}
                whileHover={{ x: 5 }}
              >
                <motion.div
                  whileHover={{ scale: 1.2, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <Check className="text-primary size-4" />
                </motion.div>
                <span className="text-muted-foreground text-sm">{feature}</span>
              </motion.li>
            ))}
          </ul>
        </CardContent>

        <CardFooter className="pt-2">
          <motion.div
            className="w-full"
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
          >
            <Button
              className={cn(
                "w-full",
                isCurrentPlan
                  ? "border-green-200 bg-green-50 text-green-700 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400"
                  : popular
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : title === "Trial" &&
                      "bg-blue-600 text-white hover:bg-blue-700"
              )}
              asChild={!isCurrentPlan}
              disabled={isCurrentPlan}
              variant={isCurrentPlan ? "outline" : "default"}
            >
              {isCurrentPlan ? (
                <div className="flex items-center justify-center gap-2">
                  <Check className="size-4" />
                  <span>Your Current Plan</span>
                </div>
              ) : (
                <a
                  href={finalButtonLink}
                  className="inline-flex items-center justify-center gap-1"
                >
                  {title === "Trial" ? (
                    <>
                      <Sparkles className="size-4" />
                      {finalButtonText}
                    </>
                  ) : (
                    finalButtonText
                  )}
                </a>
              )}
            </Button>
          </motion.div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}

interface PricingSectionProps {
  userId: string | null
  showHeader?: boolean
  showAcademicAccess?: boolean
  showFooterNote?: boolean
  currentPlan?: string | null
  trialDaysLeft?: number
  className?: string
}

export function PricingSection({
  userId,
  showHeader = true,
  showAcademicAccess = true,
  showFooterNote = true,
  currentPlan = null,
  trialDaysLeft = 14,
  className
}: PricingSectionProps) {
  const trialFeatures = [
    "14-day full access trial",
    "All optimization features",
    "Advanced analytics",
    "Standard support"
  ]

  const premiumFeatures = [
    "Unlimited access",
    "Priority support",
    "Advanced analytics",
    "Unlimited optimizations"
  ]

  return (
    <div className={cn("mx-auto", className)}>
      {showHeader && (
        <div className="mx-auto mb-12 max-w-2xl text-center">
          <h1 className="mb-4 text-4xl font-bold">
            Simple, Transparent Pricing
          </h1>
          <p className="text-muted-foreground">
            Start with our 14-day trial with full access to all features. Then
            choose the plan that best fits your needs.
          </p>
        </div>
      )}

      <div className="mx-auto grid max-w-4xl grid-cols-1 gap-8 md:grid-cols-3">
        <div id="trial-plan">
          <PricingCard
            title="Trial"
            price="Free"
            description="14-day full access evaluation"
            buttonText="Start Free Trial"
            buttonLink="#"
            features={trialFeatures}
            userId={userId}
            popular={false}
            timeLimit="14 days"
            isCurrentPlan={currentPlan === "trial"}
          />
        </div>
        <div id="premium-monthly">
          <PricingCard
            title="Premium Monthly"
            price="€79"
            description="Full access with monthly billing"
            buttonText="Subscribe Monthly"
            buttonLink={
              process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_MONTHLY || "#"
            }
            features={premiumFeatures}
            userId={userId}
            popular={false}
            timeLimit="month"
            isCurrentPlan={currentPlan === "monthly"}
          />
        </div>
        <div id="premium-yearly">
          <PricingCard
            title="Premium Yearly"
            price="€799"
            description="Save 17% with annual billing"
            buttonText="Subscribe Yearly"
            buttonLink={
              process.env.NEXT_PUBLIC_STRIPE_PAYMENT_LINK_YEARLY || "#"
            }
            features={premiumFeatures}
            userId={userId}
            popular={true}
            timeLimit="year"
            isCurrentPlan={currentPlan === "yearly"}
          />
        </div>
      </div>

      {showAcademicAccess && (
        <motion.div
          className="mt-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.3 }}
        >
          <motion.div
            className="relative inline-block overflow-hidden rounded-lg border-2 border-blue-300 bg-blue-50 p-6 dark:border-blue-800 dark:bg-blue-900/20"
            whileHover={{
              boxShadow:
                "0 10px 25px -5px rgba(59, 130, 246, 0.2), 0 8px 10px -6px rgba(59, 130, 246, 0.2)",
              borderColor: "rgba(59, 130, 246, 0.7)"
            }}
            transition={{ duration: 0.2 }}
          >
            {/* Academic decorative elements */}
            <div className="absolute -right-6 -top-6 size-12 rounded-full bg-blue-100 opacity-50 dark:bg-blue-800/30"></div>
            <div className="absolute -left-6 top-1/2 size-8 rounded-full bg-blue-100 opacity-30 dark:bg-blue-800/30"></div>
            <div className="absolute -bottom-4 right-1/3 size-10 rounded-full bg-blue-100 opacity-40 dark:bg-blue-800/30"></div>
            <div className="absolute right-0 top-0 p-1">
              <School className="size-5 text-blue-400/50" />
            </div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.3 }}
            >
              <h3 className="mb-2 flex items-center justify-center gap-2 text-lg font-semibold">
                <GraduationCap className="size-5 text-blue-600" />
                Academic Access
              </h3>
              <p className="mb-4 text-sm leading-relaxed text-blue-700 dark:text-blue-300">
                <span className="font-medium">
                  Calling all researchers and educators!
                </span>{" "}
                Eligible academic institutions qualify for our extended 90-day
                trial with full access to all premium features. Perfect for
                research projects and educational purposes.
              </p>
              <motion.div
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                <Button
                  variant="default"
                  className="bg-blue-600 font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                  asChild
                >
                  <a
                    href="/academic-signup"
                    className="flex items-center gap-2"
                  >
                    <BookOpen className="size-4" />
                    Apply for 90-Day Academic Trial
                  </a>
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      )}

      {showFooterNote && (
        <motion.p
          className="text-muted-foreground mt-8 text-center text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.3 }}
        >
          All prices are in EUR. Need a custom enterprise solution?{" "}
          <motion.a
            href="/contact"
            className="font-medium text-blue-600 transition-colors hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            whileHover={{
              textDecoration: "underline",
              textDecorationThickness: "2px",
              textUnderlineOffset: "4px"
            }}
          >
            Contact us
          </motion.a>
        </motion.p>
      )}
    </div>
  )
}
