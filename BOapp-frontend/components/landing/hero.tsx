/*
This client component provides the hero section for the landing page.
*/

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Section } from "@/components/ui/section"
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion"
import {
  Rocket,
  LayoutDashboard,
  ArrowRight,
  Zap,
  LineChart,
  Lightbulb,
  Target
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { SignedIn, SignedOut } from "@clerk/nextjs"
import { useRef, useState } from "react"
import { useLoading } from "@/contexts/loading-context"

// Animation variants
const titleVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.8,
      delay: 0.4,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const subtitleVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      delay: 0.6,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const buttonVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      delay: 0.8,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  },
  hover: {
    scale: 1.05,
    boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.5)",
    transition: {
      duration: 0.3,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  },
  tap: {
    scale: 0.98,
    boxShadow: "0 5px 15px -5px rgba(59, 130, 246, 0.4)",
    transition: {
      duration: 0.15,
      ease: [0.25, 0.1, 0.25, 1.0]
    }
  }
}

const featureBadgeVariants = {
  hidden: { opacity: 0, scale: 0.8, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.5,
      delay: 0.8 + i * 0.1,
      ease: "easeOut"
    }
  }),
  hover: {
    scale: 1.05,
    y: -5,
    boxShadow: "0 10px 25px -5px rgba(59, 130, 246, 0.2)",
    transition: { duration: 0.2 }
  }
}

// Key features to highlight
const keyFeatures = [
  {
    icon: <Zap className="size-4 text-blue-500" />,
    text: "AI-Powered"
  },
  {
    icon: <LineChart className="size-4 text-indigo-500" />,
    text: "Data-Driven"
  },
  {
    icon: <Lightbulb className="size-4 text-purple-500" />,
    text: "Intelligent"
  },
  {
    icon: <Target className="size-4 text-pink-500" />,
    text: "Precise"
  }
]

export const HeroSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null)
  const [isHovering, setIsHovering] = useState(false)
  const router = useRouter()
  const { startLoading } = useLoading()
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"]
  })

  // Handle dashboard navigation with loading overlay
  const handleDashboardClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string
  ) => {
    e.preventDefault()
    startLoading()

    // Add a small delay before navigation to ensure the loading overlay appears
    setTimeout(() => {
      router.push(path)
    }, 100) // 100ms delay (reduced from 300ms)
  }

  // Parallax effects
  const titleY = useTransform(scrollYProgress, [0, 0.5], [0, -30])

  return (
    <Section
      ref={sectionRef}
      className="pb-0 pt-20 md:pt-28"
      containerSize="lg"
      containerClassName="text-center"
    >
      <div className="hero-content flex flex-col items-center justify-center">
        <motion.div
          initial="hidden"
          animate="visible"
          className="mx-auto flex max-w-2xl flex-col items-center justify-center gap-6"
          style={{ y: titleY }}
        >
          {/* Animated badge above title */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-2"
          >
            <span className="bg-primary/10 text-primary inline-flex items-center rounded-full px-3 py-1 text-sm font-medium backdrop-blur-sm">
              <span className="mr-1 flex size-2">
                <span className="bg-primary absolute inline-flex size-2 animate-ping rounded-full opacity-75"></span>
                <span className="bg-primary relative inline-flex size-2 rounded-full"></span>
              </span>
              New: Advanced Optimization Engine
            </span>
          </motion.div>

          <motion.div
            variants={titleVariants}
            className="relative text-balance text-5xl font-bold md:text-7xl"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            <span className="from-primary bg-gradient-to-r to-blue-600 bg-clip-text text-transparent">
              Optimizer™
            </span>
            {/* Subtle icon animations on hover */}
            <AnimatePresence>
              {isHovering && (
                <>
                  <motion.div
                    className="absolute -right-6 top-1/2 -translate-y-1/2 text-blue-500/70"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Target className="size-5" />
                  </motion.div>
                  <motion.div
                    className="absolute -left-6 top-1/2 -translate-y-1/2 text-indigo-500/70"
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <LineChart className="size-5" />
                  </motion.div>
                </>
              )}
            </AnimatePresence>
          </motion.div>

          <motion.div
            variants={subtitleVariants}
            className="text-muted-foreground max-w-xl text-balance text-lg md:text-xl"
          >
            <span className="text-foreground font-medium">
              Revolutionize your research
            </span>{" "}
            with intelligent experimental design that delivers breakthrough
            results
            <span className="text-blue-500"> up to 70% faster</span>.
          </motion.div>

          {/* Feature badges */}
          <motion.div className="mt-2 flex flex-wrap justify-center gap-2">
            {keyFeatures.map((feature, i) => (
              <motion.div
                key={i}
                custom={i}
                variants={featureBadgeVariants}
                whileHover="hover"
                className="bg-background/50 flex items-center gap-1.5 rounded-full border px-3 py-1 text-sm backdrop-blur-sm"
              >
                {feature.icon}
                {feature.text}
              </motion.div>
            ))}
          </motion.div>

          <motion.div variants={buttonVariants} className="mt-2 flex gap-4">
            <SignedOut>
              <Link href="/signup" prefetch={false}>
                <motion.div whileHover="hover" whileTap="tap">
                  <Button className="from-primary hover:from-primary/90 min-w-40 bg-gradient-to-r to-blue-600 px-6 py-5 text-base font-semibold shadow-lg shadow-blue-500/30 hover:to-blue-700 md:min-w-48 md:px-8 md:py-6 md:text-lg">
                    <Rocket className="mr-2 size-5" />
                    Start for Free
                    <ArrowRight className="ml-2 size-4" />
                  </Button>
                </motion.div>
              </Link>
            </SignedOut>

            <SignedIn>
              <Link
                href="/dashboard/optimizations"
                prefetch={false}
                onClick={e =>
                  handleDashboardClick(e, "/dashboard/optimizations")
                }
              >
                <motion.div whileHover="hover" whileTap="tap">
                  <Button className="from-primary hover:from-primary/90 min-w-40 bg-gradient-to-r to-blue-600 px-6 py-5 text-base font-semibold shadow-lg shadow-blue-500/30 hover:to-blue-700 md:min-w-48 md:px-8 md:py-6 md:text-lg">
                    <LayoutDashboard className="mr-2 size-5" />
                    Go to Dashboard
                  </Button>
                </motion.div>
              </Link>
            </SignedIn>
          </motion.div>

          {/* Social proof */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="text-muted-foreground mt-4 text-sm"
          >
            Trusted by leading research teams at
            <div className="mt-2 flex flex-wrap items-center justify-center gap-4 opacity-70">
              <span className="font-semibold">Stanford</span>
              <span>•</span>
              <span className="font-semibold">MIT</span>
              <span>•</span>
              <span className="font-semibold">Cambridge</span>
              <span>•</span>
              <span className="font-semibold">ETH Zurich</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </Section>
  )
}
