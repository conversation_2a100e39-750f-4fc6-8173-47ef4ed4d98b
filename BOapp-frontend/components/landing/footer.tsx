/*
This client component provides a simplified footer for the app with a brand watermark.
*/

"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"

export function Footer() {
  const [windowWidth, setWindowWidth] = useState(0)

  // Update window width on client side
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    // Set initial width
    handleResize()

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Clean up
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Calculate appropriate dimensions for the brand name
  const fontSize = Math.max(windowWidth * 0.1, 70) // Smaller font size to ensure it fits
  const footerHeight = Math.max(fontSize * 1.5, 140) // Taller footer to accommodate the text

  return (
    <footer
      className="relative flex flex-col overflow-hidden border-t"
      style={{ minHeight: `${footerHeight}px` }}
    >
      {/* Brand name watermark */}
      <div className="pointer-events-none absolute inset-0 flex items-center justify-center overflow-visible">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.03 }}
          transition={{ duration: 1 }}
          className="text-primary whitespace-nowrap px-4 font-bold"
          style={{
            fontSize: `${fontSize}px`,
            width: "100%",
            textAlign: "center",
            letterSpacing: "0.05em" // Add slight letter spacing for better readability
          }}
        >
          Optimizer™
        </motion.div>
      </div>

      {/* Empty flex spacer to push copyright to bottom */}
      <div className="grow"></div>

      {/* Copyright notice at the bottom */}
      <div className="text-muted-foreground relative z-10 py-4 text-center">
        <p>&copy; {new Date().getFullYear()} SynSilico. All rights reserved.</p>
      </div>
    </footer>
  )
}
