"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON>, FlaskConical, Rocket, ArrowRight, Gauge } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Section } from "@/components/ui/section"
import Link from "next/link"

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 10
    }
  }
}

// User types with their benefits
const userTypes = [
  {
    title: "Research Scientists",
    description:
      "Stretch limited lab resources further with smart experiment designs that deliver maximum insights from every test.",
    icon: <Beaker className="text-primary size-8" />
  },
  {
    title: "Materials Scientists",
    description:
      "Explore complex material designs efficiently to discover novel materials with targeted properties, cutting development timelines by 40-60%.",
    icon: <FlaskConical className="text-primary size-8" />
  },
  {
    title: "Process Engineers",
    description:
      "Scale lab discoveries to production with robust optimization that tackles real-world constraints and variability.",
    icon: <Gauge className="text-primary size-8" />
  },
  {
    title: "R&D Teams",
    description:
      "Accelerate innovation cycles and reduce time-to-market with data-driven optimization strategies.",
    icon: <Rocket className="text-primary size-8" />
  }
]

export function FeatureShowcase() {
  return (
    <Section className="from-background bg-gradient-to-b to-gray-50/50 py-16 md:py-24">
      <div className="container mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12 text-center"
        >
          <h2 className="from-primary mb-4 bg-gradient-to-r to-blue-600 bg-clip-text text-3xl font-bold text-transparent md:text-4xl">
            Designed For Your Success
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-base md:text-lg">
            Our platform is built to address the specific needs of researchers
            and engineers working on complex optimization challenges.
          </p>
        </motion.div>

        {/* For You Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-16"
        >
          <Card className="overflow-hidden border bg-gradient-to-b from-white to-gray-50/80 shadow-lg">
            <CardContent className="p-6 md:p-8">
              <h3 className="from-primary mb-8 bg-gradient-to-r to-blue-600 bg-clip-text text-center text-2xl font-semibold text-transparent">
                Why Choose Our Platform?
              </h3>
              <div className="grid gap-8 md:grid-cols-2">
                {userTypes.map((userType, index) => (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    whileHover={{ scale: 1.03 }}
                    className="rounded-xl border bg-gradient-to-br from-white to-gray-50 p-5 shadow-sm transition-all hover:shadow-md"
                  >
                    <div className="mb-4 flex items-center gap-4">
                      <div className="bg-primary/10 flex size-16 items-center justify-center rounded-full">
                        {userType.icon}
                      </div>
                      <h4 className="text-lg font-medium">{userType.title}</h4>
                    </div>
                    <p className="text-muted-foreground">
                      {userType.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            duration: 0.8,
            delay: 0.3,
            type: "spring",
            stiffness: 100,
            damping: 15
          }}
          className="text-center"
        >
          <Card className="from-primary/10 via-primary/5 to-background mx-auto max-w-3xl overflow-hidden border bg-gradient-to-r shadow-xl">
            <CardContent className="p-8 md:p-10">
              <h3 className="mb-4 text-2xl font-semibold md:text-3xl">
                Ready to Transform Your Research?
              </h3>
              <p className="text-muted-foreground mx-auto mb-8 max-w-xl text-base md:text-lg">
                Join leading labs accelerating discovery with us. Explore
                Optimizer with complementary rights by accessing the Dashboard.
              </p>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-block"
              >
                <Button
                  asChild
                  size="lg"
                  className="from-primary hover:from-primary/90 group bg-gradient-to-r to-blue-600 px-8 py-6 text-lg shadow-lg transition-all hover:to-blue-700 hover:shadow-xl"
                >
                  <Link href="/dashboard">
                    Get Started
                    <ArrowRight className="ml-2 size-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </Section>
  )
}
