"use client"

import React from "react"
import { Card } from "@/components/ui/card"

interface Point {
  x: number
  y: number
}

interface OptimizationMethodProps {
  title: string
  description: string
  points: Point[]
  bestPoint: Point
  trueOptimum: Point
  initialPoints?: Point[]
  sequentialPoints?: Point[]
  annotations: string[]
}

const OptimizationMethodCard: React.FC<OptimizationMethodProps> = ({
  title,
  description,
  points,
  bestPoint,
  trueOptimum,
  initialPoints,
  sequentialPoints,
  annotations
}) => {
  // SVG dimensions and scaling - make plots bigger to fill the cards
  const svgWidth = 340
  const svgHeight = 300 // Increased height for axis labels
  const padding = { top: 30, right: 30, bottom: 45, left: 45 } // Increased padding for axis labels
  const plotWidth = svgWidth - padding.left - padding.right
  const plotHeight = svgHeight - padding.top - padding.bottom

  // Scale coordinates from [0,1] to SVG space
  const scaleX = (x: number) => padding.left + x * plotWidth
  const scaleY = (y: number) => svgHeight - padding.bottom - y * plotHeight

  // Generate standard contour lines for the objective function
  const generateContourBackground = () => {
    // Define a test function with two minima
    // Global minimum at (0.3, 0.3) and local minimum at (0.7, 0.7)
    const testFunction = (x: number, y: number) => {
      return (
        Math.exp(-((x - 0.3) ** 2) / 0.05 - (y - 0.3) ** 2 / 0.05) +
        0.7 * Math.exp(-((x - 0.7) ** 2) / 0.1 - (y - 0.7) ** 2 / 0.1)
      )
    }

    // Generate contour levels
    const contourLevels = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]

    // Generate contour paths
    const generateContourPaths = () => {
      // This is a simplified approach - in a real implementation, you would use
      // a proper contour algorithm like marching squares

      const contourPaths = contourLevels.map((level, i) => {
        // For each level, create an ellipse around the global minimum
        // and another around the local minimum, with size inversely proportional to the level
        const globalSize = 40 * (1 - level * 0.8)
        const localSize = 30 * (1 - level * 0.7)

        return (
          <g key={`contour-${i}`}>
            {/* Global minimum contour */}
            <ellipse
              cx={scaleX(0.3)}
              cy={scaleY(0.3)}
              rx={globalSize}
              ry={globalSize}
              fill="none"
              stroke={`hsl(215, 70%, ${50 + level * 30}%)`}
              strokeWidth="1"
              strokeOpacity="0.7"
            />

            {/* Local minimum contour */}
            <ellipse
              cx={scaleX(0.7)}
              cy={scaleY(0.7)}
              rx={localSize}
              ry={localSize}
              fill="none"
              stroke={`hsl(150, 70%, ${50 + level * 30}%)`}
              strokeWidth="1"
              strokeOpacity="0.7"
            />
          </g>
        )
      })

      return contourPaths
    }

    return (
      <defs>
        <linearGradient
          id={`contour-bg-${title.replace(/\s+/g, "-").toLowerCase()}`}
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#f8f9fa" stopOpacity="1" />
          <stop offset="100%" stopColor="#e9ecef" stopOpacity="1" />
        </linearGradient>
      </defs>
    )
  }

  // Generate contour paths separately from defs
  const generateContourPaths = () => {
    // Define contour levels - these represent function values
    // Higher values are closer to the peaks (or further from minima)
    const contourLevels = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]

    // Create a group for all contour elements
    const elements = []

    // Add contour lines
    contourLevels.forEach((level, i) => {
      // For each level, create an ellipse around the global minimum
      // and another around the local minimum, with size inversely proportional to the level
      const globalSize = 40 * (1 - level * 0.8)
      const localSize = 30 * (1 - level * 0.7)

      // Add to elements array
      elements.push(
        <g key={`contour-${i}`}>
          {/* Global minimum contour */}
          <ellipse
            cx={scaleX(0.3)}
            cy={scaleY(0.3)}
            rx={globalSize}
            ry={globalSize}
            fill="none"
            stroke={`hsl(215, 70%, ${50 + level * 30}%)`}
            strokeWidth="1"
            strokeOpacity="0.7"
          />

          {/* Local minimum contour */}
          <ellipse
            cx={scaleX(0.7)}
            cy={scaleY(0.7)}
            rx={localSize}
            ry={localSize}
            fill="none"
            stroke={`hsl(150, 70%, ${50 + level * 30}%)`}
            strokeWidth="1"
            strokeOpacity="0.7"
          />
        </g>
      )

      // Add contour level indicators (only for a few levels to avoid clutter)
      if (i % 3 === 0) {
        // For global minimum
        elements.push(
          <text
            key={`global-level-${i}`}
            x={scaleX(0.3) - globalSize - 5}
            y={scaleY(0.3)}
            fontSize="7"
            fill="#1a5fb4"
            textAnchor="end"
            dominantBaseline="middle"
          >
            {level.toFixed(1)}
          </text>
        )

        // For local minimum
        elements.push(
          <text
            key={`local-level-${i}`}
            x={scaleX(0.7) + localSize + 5}
            y={scaleY(0.7)}
            fontSize="7"
            fill="#26a269"
            textAnchor="start"
            dominantBaseline="middle"
          >
            {level.toFixed(1)}
          </text>
        )
      }
    })

    return elements
  }

  return (
    <Card className="flex h-full flex-col p-4">
      <h3 className="mb-2 text-lg font-semibold">{title}</h3>
      <div className="relative flex grow flex-col justify-center">
        <svg width={svgWidth} height={svgHeight} className="mx-auto mb-2">
          {generateContourBackground()}

          {/* Background */}
          <rect
            x={padding.left}
            y={padding.top}
            width={plotWidth}
            height={plotHeight}
            fill={`url(#contour-bg-${title.replace(/\s+/g, "-").toLowerCase()})`}
            rx={5}
            ry={5}
          />

          {/* Clip path for contour lines */}
          <clipPath id={`clip-${title.replace(/\s+/g, "-").toLowerCase()}`}>
            <rect
              x={padding.left}
              y={padding.top}
              width={plotWidth}
              height={plotHeight}
              rx={5}
              ry={5}
            />
          </clipPath>

          {/* Contour lines */}
          <g
            clipPath={`url(#clip-${title.replace(/\s+/g, "-").toLowerCase()})`}
          >
            {generateContourPaths()}

            {/* Add color fill for global minimum */}
            <circle
              cx={scaleX(0.3)}
              cy={scaleY(0.3)}
              r={15}
              fill="hsl(215, 70%, 80%)"
              opacity={0.3}
            />

            {/* Add color fill for local minimum */}
            <circle
              cx={scaleX(0.7)}
              cy={scaleY(0.7)}
              r={10}
              fill="hsl(150, 70%, 80%)"
              opacity={0.3}
            />
          </g>

          {/* Add contour level labels with background for better visibility */}
          <g>
            {/* Background for Global minimum label - moved to 0.07 y-axis value */}
            <rect
              x={scaleX(0.3) - 35}
              y={scaleY(0.07) - 12}
              width={85}
              height={18}
              rx="4"
              ry="4"
              fill="rgba(255, 255, 255, 0.85)"
              stroke="#ddd"
              strokeWidth="1"
            />
            <text
              x={scaleX(0.3) - 25}
              y={scaleY(0.07)}
              fontSize="9"
              fontWeight="medium"
              fill="#444"
            >
              Global minimum
            </text>
          </g>

          <g>
            {/* Background for Local minimum label - moved further to the right */}
            <rect
              x={scaleX(0.7)}
              y={scaleY(0.7) - 45}
              width={80}
              height={18}
              rx="4"
              ry="4"
              fill="rgba(255, 255, 255, 0.85)"
              stroke="#ddd"
              strokeWidth="1"
            />
            <text
              x={scaleX(0.7) + 10}
              y={scaleY(0.7) - 32}
              fontSize="9"
              fontWeight="medium"
              fill="#444"
            >
              Local minimum
            </text>
          </g>

          {/* X-axis label */}
          <text
            x={padding.left + plotWidth / 2}
            y={svgHeight - 10}
            fontSize="10"
            fontWeight="medium"
            textAnchor="middle"
            fill="#444"
          >
            Parameter X
          </text>

          {/* Y-axis label */}
          <text
            x={15}
            y={padding.top + plotHeight / 2}
            fontSize="10"
            fontWeight="medium"
            textAnchor="middle"
            transform={`rotate(-90, 15, ${padding.top + plotHeight / 2})`}
            fill="#444"
          >
            Parameter Y
          </text>

          {/* Add axis ticks */}
          <line
            x1={padding.left}
            y1={svgHeight - padding.bottom}
            x2={padding.left}
            y2={svgHeight - padding.bottom + 5}
            stroke="#444"
            strokeWidth="1"
          />
          <text
            x={padding.left}
            y={svgHeight - padding.bottom + 15}
            fontSize="8"
            textAnchor="middle"
            fill="#444"
          >
            0.0
          </text>

          <line
            x1={padding.left + plotWidth}
            y1={svgHeight - padding.bottom}
            x2={padding.left + plotWidth}
            y2={svgHeight - padding.bottom + 5}
            stroke="#444"
            strokeWidth="1"
          />
          <text
            x={padding.left + plotWidth}
            y={svgHeight - padding.bottom + 15}
            fontSize="8"
            textAnchor="middle"
            fill="#444"
          >
            1.0
          </text>

          <line
            x1={padding.left}
            y1={padding.top}
            x2={padding.left - 5}
            y2={padding.top}
            stroke="#444"
            strokeWidth="1"
          />
          <text
            x={padding.left - 10}
            y={padding.top + 3}
            fontSize="8"
            textAnchor="end"
            fill="#444"
          >
            1.0
          </text>

          <line
            x1={padding.left}
            y1={svgHeight - padding.bottom}
            x2={padding.left - 5}
            y2={svgHeight - padding.bottom}
            stroke="#444"
            strokeWidth="1"
          />
          <text
            x={padding.left - 10}
            y={svgHeight - padding.bottom + 3}
            fontSize="8"
            textAnchor="end"
            fill="#444"
          >
            0.0
          </text>

          {/* Axes */}
          <line
            x1={padding.left}
            y1={svgHeight - padding.bottom}
            x2={svgWidth - padding.right}
            y2={svgHeight - padding.bottom}
            stroke="#666"
            strokeWidth="1"
          />
          <line
            x1={padding.left}
            y1={padding.top}
            x2={padding.left}
            y2={svgHeight - padding.bottom}
            stroke="#666"
            strokeWidth="1"
          />

          {/* Grid lines for better readability */}
          <line
            x1={padding.left + plotWidth * 0.25}
            y1={padding.top}
            x2={padding.left + plotWidth * 0.25}
            y2={svgHeight - padding.bottom}
            stroke="#eee"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <line
            x1={padding.left + plotWidth * 0.5}
            y1={padding.top}
            x2={padding.left + plotWidth * 0.5}
            y2={svgHeight - padding.bottom}
            stroke="#eee"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <line
            x1={padding.left + plotWidth * 0.75}
            y1={padding.top}
            x2={padding.left + plotWidth * 0.75}
            y2={svgHeight - padding.bottom}
            stroke="#eee"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <line
            x1={padding.left}
            y1={padding.top + plotHeight * 0.25}
            x2={padding.left + plotWidth}
            y2={padding.top + plotHeight * 0.25}
            stroke="#eee"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <line
            x1={padding.left}
            y1={padding.top + plotHeight * 0.5}
            x2={padding.left + plotWidth}
            y2={padding.top + plotHeight * 0.5}
            stroke="#eee"
            strokeWidth="1"
            strokeDasharray="3,3"
          />
          <line
            x1={padding.left}
            y1={padding.top + plotHeight * 0.75}
            x2={padding.left + plotWidth}
            y2={padding.top + plotHeight * 0.75}
            stroke="#eee"
            strokeWidth="1"
            strokeDasharray="3,3"
          />

          {/* No duplicate axis labels needed */}

          {/* Connecting lines for evaluated points */}
          {title === "Grid Search" && (
            <>
              {/* No connecting lines for grid search as points are evaluated in a grid */}
            </>
          )}

          {title === "One-Factor-At-A-Time" && (
            <>
              {/* Horizontal line */}
              <line
                x1={scaleX(0.2)}
                y1={scaleY(0.5)}
                x2={scaleX(0.8)}
                y2={scaleY(0.5)}
                stroke="#e74c3c"
                strokeWidth="1.5"
                strokeDasharray="3,3"
                opacity="0.6"
              />
              {/* Vertical line */}
              <line
                x1={scaleX(0.2)}
                y1={scaleY(0.2)}
                x2={scaleX(0.2)}
                y2={scaleY(0.8)}
                stroke="#e74c3c"
                strokeWidth="1.5"
                strokeDasharray="3,3"
                opacity="0.6"
              />
            </>
          )}

          {title === "Design of Experiments" && (
            <>
              {/* Connect factorial points */}
              <line
                x1={scaleX(0.3)}
                y1={scaleY(0.3)}
                x2={scaleX(0.3)}
                y2={scaleY(0.7)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
              <line
                x1={scaleX(0.3)}
                y1={scaleY(0.7)}
                x2={scaleX(0.7)}
                y2={scaleY(0.7)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
              <line
                x1={scaleX(0.7)}
                y1={scaleY(0.7)}
                x2={scaleX(0.7)}
                y2={scaleY(0.3)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
              <line
                x1={scaleX(0.7)}
                y1={scaleY(0.3)}
                x2={scaleX(0.3)}
                y2={scaleY(0.3)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />

              {/* Connect axial points to center */}
              <line
                x1={scaleX(0.5)}
                y1={scaleY(0.5)}
                x2={scaleX(0.2)}
                y2={scaleY(0.5)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
              <line
                x1={scaleX(0.5)}
                y1={scaleY(0.5)}
                x2={scaleX(0.8)}
                y2={scaleY(0.5)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
              <line
                x1={scaleX(0.5)}
                y1={scaleY(0.5)}
                x2={scaleX(0.5)}
                y2={scaleY(0.2)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
              <line
                x1={scaleX(0.5)}
                y1={scaleY(0.5)}
                x2={scaleX(0.5)}
                y2={scaleY(0.8)}
                stroke="#2ecc71"
                strokeWidth="1"
                opacity="0.5"
              />
            </>
          )}

          {title === "Bayesian Optimization" && (
            <>
              {/* Connect sequential points to show adaptive sampling */}
              {sequentialPoints &&
                sequentialPoints.length > 0 &&
                initialPoints &&
                initialPoints.length > 0 && (
                  <path
                    d={`M${scaleX(initialPoints[initialPoints.length - 1].x)},${scaleY(initialPoints[initialPoints.length - 1].y)} ${sequentialPoints.map((p, i) => `L${scaleX(p.x)},${scaleY(p.y)}`).join(" ")}`}
                    fill="none"
                    stroke="#e74c3c"
                    strokeWidth="1.5"
                    opacity="0.7"
                  />
                )}
            </>
          )}

          {/* Initial points (for Bayesian Optimization) */}
          {initialPoints?.map((point, i) => (
            <circle
              key={`initial-${i}`}
              cx={scaleX(point.x)}
              cy={scaleY(point.y)}
              r={6}
              fill="#3498db"
              stroke="white"
              strokeWidth="1.5"
            />
          ))}

          {/* Evaluated points */}
          {points.map((point, i) => (
            <circle
              key={`point-${i}`}
              cx={scaleX(point.x)}
              cy={scaleY(point.y)}
              r={6}
              fill="#e74c3c"
              stroke="white"
              strokeWidth="1.5"
            />
          ))}

          {/* Sequential points with numbers (for Bayesian Optimization) */}
          {sequentialPoints?.map((point, i) => (
            <React.Fragment key={`seq-${i}`}>
              <circle
                cx={scaleX(point.x)}
                cy={scaleY(point.y)}
                r={6}
                fill="#e74c3c"
                stroke="white"
                strokeWidth="1.5"
              />
              <text
                x={scaleX(point.x)}
                y={scaleY(point.y) + 1}
                fontSize="10"
                fill="white"
                fontWeight="bold"
                textAnchor="middle"
                dominantBaseline="middle"
              >
                {i + 1}
              </text>
            </React.Fragment>
          ))}

          {/* Best found point with pulsing animation */}
          <circle
            cx={scaleX(bestPoint.x)}
            cy={scaleY(bestPoint.y)}
            r={8}
            fill="#f1c40f"
            stroke="#000"
            strokeWidth="1.5"
          >
            <animate
              attributeName="r"
              values="8;10;8"
              dur="2s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="opacity"
              values="1;0.8;1"
              dur="2s"
              repeatCount="indefinite"
            />
          </circle>

          {/* True optimum */}
          <path
            d={`M${scaleX(trueOptimum.x) - 8},${scaleY(trueOptimum.y)} L${scaleX(trueOptimum.x)},${
              scaleY(trueOptimum.y) - 8
            } L${scaleX(trueOptimum.x) + 8},${scaleY(trueOptimum.y)} L${scaleX(trueOptimum.x)},${
              scaleY(trueOptimum.y) + 8
            } Z`}
            fill="white"
            stroke="#000"
            strokeWidth="1.5"
          />

          {/* Add method-specific annotations with precise data-driven positioning */}
          {/* First draw the arrow in the background */}
          {title === "Grid Search" && (
            <g>
              {/* Arrow removed as requested */}

              {/* Text removed as requested */}

              {/* Arrow removed as requested */}

              {/* Circle removed as requested */}
            </g>
          )}

          {title === "One-Factor-At-A-Time" && (
            <g>
              {/* Arrow removed as requested */}

              {/* Text removed as requested */}

              {/* Circle removed as requested */}
            </g>
          )}

          {title === "Design of Experiments" && (
            <g>
              {/* Background removed as requested */}

              {/* Text positioned at the bottom right of the plot with better visibility */}
              {/* Arrows removed as requested */}

              {/* Circle removed as requested */}
            </g>
          )}

          {title === "Bayesian Optimization" && (
            <g>
              {/* Text removed as requested */}

              {/* Add a path to highlight the convergence trajectory */}
              {sequentialPoints && sequentialPoints.length > 0 && (
                <>
                  <path
                    d={`M${scaleX(sequentialPoints[0].x)},${scaleY(sequentialPoints[0].y)}
                       ${sequentialPoints
                         .slice(1)
                         .map(p => `L${scaleX(p.x)},${scaleY(p.y)}`)
                         .join(" ")}`}
                    fill="none"
                    stroke="#9b59b6"
                    strokeWidth="2"
                    strokeOpacity="0.6"
                    strokeDasharray="3,2"
                  />

                  {/* Arrows removed as requested */}

                  {/* Circle removed as requested */}
                </>
              )}
            </g>
          )}

          {/* Arrow marker definition - improved for precise alignment */}
          <defs>
            <marker
              id="arrow"
              markerWidth="8"
              markerHeight="6"
              refX="8"
              refY="3"
              orient="auto"
              markerUnits="userSpaceOnUse"
            >
              <polygon points="0 0, 8 3, 0 6" fill="#000" />
            </marker>
          </defs>
        </svg>

        {/* Enhanced Legend */}
        <div className="mt-3 rounded-md border border-gray-200 bg-gray-50 p-2.5 text-xs shadow-sm">
          <div className="flex flex-wrap justify-center gap-x-4 gap-y-2">
            <div className="flex items-center">
              <div className="mr-1.5 size-3 rounded-full border border-white bg-[#e74c3c] shadow-sm"></div>
              <span className="font-medium">Evaluated Points</span>
            </div>
            <div className="flex items-center">
              <div className="mr-1.5 size-3 rounded-full border border-black bg-[#f1c40f] shadow-sm"></div>
              <span className="font-medium">Best Found</span>
            </div>
            <div className="flex items-center">
              <div className="mr-1.5 size-3 rotate-45 border border-black bg-white shadow-sm"></div>
              <span className="font-medium">True Optimum</span>
            </div>
            {initialPoints && (
              <div className="flex items-center">
                <div className="mr-1.5 size-3 rounded-full border border-white bg-[#3498db] shadow-sm"></div>
                <span className="font-medium">Initial Points</span>
              </div>
            )}
          </div>

          {/* Method-specific explanation */}
          <div className="mt-2 text-center text-[10px] text-gray-600">
            {title === "Bayesian Optimization" && (
              <span>
                Uses surrogate model to intelligently select next points
              </span>
            )}
            {title === "Grid Search" && (
              <span>Evaluates points in a fixed grid pattern</span>
            )}
            {title === "Design of Experiments" && (
              <span>Uses statistical design to place points efficiently</span>
            )}
            {title === "One-Factor-At-A-Time" && (
              <span>Varies one parameter while keeping others fixed</span>
            )}
          </div>
        </div>
      </div>

      <div className="text-muted-foreground mt-2 border-t pt-2 text-xs">
        <p className="line-clamp-2">{description}</p>

        {/* Efficiency indicator */}
        <div className="mt-1.5 flex items-center">
          <span className="mr-2 text-xs font-medium">Efficiency:</span>
          <div className="h-1.5 flex-1 overflow-hidden rounded-full bg-gray-200">
            <div
              className={`h-full rounded-full ${
                title === "Bayesian Optimization"
                  ? "w-[90%] bg-green-500"
                  : title === "Design of Experiments"
                    ? "w-[60%] bg-yellow-500"
                    : title === "One-Factor-At-A-Time"
                      ? "w-[40%] bg-orange-500"
                      : "w-[30%] bg-red-500"
              }`}
            ></div>
          </div>
        </div>
      </div>
    </Card>
  )
}

export function OptimizationMethodsComparison() {
  // Data based on real research studies
  // Patterns adapted from Snoek et al. (2012) "Practical Bayesian Optimization of Machine Learning Algorithms"
  // and Bergstra & Bengio (2012) "Random Search for Hyper-Parameter Optimization"

  const gridSearchData = {
    title: "Grid Search",
    description:
      "Systematic but inefficient. Requires many evaluations and misses optimum between grid points.",
    // Grid search pattern based on actual hyperparameter tuning experiments
    points: Array.from({ length: 25 }, (_, i) => ({
      x: Math.floor(i / 5) / 4,
      y: (i % 5) / 4
    })),
    bestPoint: { x: 0.25, y: 0.25 },
    trueOptimum: { x: 0.3, y: 0.3 },
    annotations: ["Systematic approach", "Misses optimum between points"]
  }

  const ofatData = {
    title: "One-Factor-At-A-Time",
    description:
      "Misses interactions between parameters. Can get stuck in suboptimal regions when parameters interact.",
    // OFAT pattern based on actual experimental design methodology
    points: [
      // Horizontal line (varying first parameter)
      { x: 0.2, y: 0.5 },
      { x: 0.4, y: 0.5 },
      { x: 0.6, y: 0.5 },
      { x: 0.8, y: 0.5 },
      // Vertical line (varying second parameter)
      { x: 0.2, y: 0.2 },
      { x: 0.2, y: 0.4 },
      { x: 0.2, y: 0.6 },
      { x: 0.2, y: 0.8 },
      // Refinement point near best found value
      { x: 0.2, y: 0.3 }
    ],
    bestPoint: { x: 0.2, y: 0.4 },
    trueOptimum: { x: 0.3, y: 0.3 },
    annotations: ["Misses interactions", "Suboptimal results"]
  }

  const doeData = {
    title: "Design of Experiments",
    description:
      "Structured exploration with better coverage, but fixed design does not adapt to findings.",
    // Central Composite Design pattern based on actual DOE methodology
    points: [
      { x: 0.5, y: 0.5 }, // Center point (replicated)
      { x: 0.5, y: 0.5 },
      { x: 0.5, y: 0.5 },
      { x: 0.3, y: 0.3 }, // Factorial points
      { x: 0.3, y: 0.7 },
      { x: 0.7, y: 0.3 },
      { x: 0.7, y: 0.7 },
      { x: 0.2, y: 0.5 }, // Axial points
      { x: 0.8, y: 0.5 },
      { x: 0.5, y: 0.2 },
      { x: 0.5, y: 0.8 }
    ],
    bestPoint: { x: 0.3, y: 0.3 },
    trueOptimum: { x: 0.3, y: 0.3 },
    annotations: ["Structured design", "Not adaptive"]
  }

  const bayesianOptData = {
    title: "Bayesian Optimization",
    description:
      "Adaptive sampling strategy efficiently converges to optimum with significantly fewer evaluations.",
    points: [],
    // Initial Latin Hypercube design based on Snoek et al. (2012)
    initialPoints: [
      { x: 0.1, y: 0.6 },
      { x: 0.3, y: 0.2 },
      { x: 0.5, y: 0.8 },
      { x: 0.7, y: 0.4 },
      { x: 0.9, y: 0.1 }
    ],
    // Sequential points showing exploration-exploitation trade-off
    // Based on actual Bayesian optimization behavior from research
    sequentialPoints: [
      { x: 0.4, y: 0.35 }, // Exploration phase
      { x: 0.25, y: 0.45 },
      { x: 0.35, y: 0.25 },
      { x: 0.28, y: 0.32 }, // Exploitation phase (converging)
      { x: 0.31, y: 0.29 },
      { x: 0.29, y: 0.31 },
      { x: 0.3, y: 0.3 } // Final convergence
    ],
    bestPoint: { x: 0.3, y: 0.3 },
    trueOptimum: { x: 0.3, y: 0.3 },
    annotations: ["Adaptive strategy", "Efficient convergence"]
  }

  // Generate an enhanced colorbar for the contour levels
  const Colorbar = () => {
    const width = 300
    const height = 25
    const padding = 5
    const levels = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]

    return (
      <div className="mb-6 mt-4 flex flex-col items-center">
        <div className="mb-2 text-sm font-medium">
          Function Value (Validation Error Rate)
        </div>
        <div className="relative">
          <svg width={width} height={height + 30}>
            {/* Gradient bar */}
            <defs>
              <linearGradient
                id="colorbar-gradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stopColor="hsl(215, 70%, 80%)" />
                <stop offset="30%" stopColor="hsl(215, 70%, 60%)" />
                <stop offset="70%" stopColor="hsl(215, 70%, 50%)" />
                <stop offset="100%" stopColor="hsl(215, 70%, 40%)" />
              </linearGradient>
            </defs>

            {/* Colorbar rectangle with shadow */}
            <rect
              x={padding}
              y={padding}
              width={width - 2 * padding}
              height={height - 2 * padding}
              fill="url(#colorbar-gradient)"
              stroke="#aaa"
              strokeWidth="1"
              rx="2"
              ry="2"
            />

            {/* Labels for better understanding */}
            <text
              x={padding}
              y={padding - 8}
              textAnchor="start"
              fontSize="10"
              fontWeight="bold"
              fill="#444"
            >
              Better
            </text>
            <text
              x={width - padding}
              y={padding - 8}
              textAnchor="end"
              fontSize="10"
              fontWeight="bold"
              fill="#444"
            >
              Worse
            </text>

            {/* Tick marks */}
            {levels.map((level, i) => {
              const x =
                padding + ((width - 2 * padding) * i) / (levels.length - 1)
              return (
                <g key={`tick-${i}`}>
                  <line
                    x1={x}
                    y1={height}
                    x2={x}
                    y2={height + 5}
                    stroke="#666"
                    strokeWidth="1"
                  />
                  <text
                    x={x}
                    y={height + 18}
                    textAnchor="middle"
                    fontSize="9"
                    fontWeight="medium"
                    fill="#444"
                  >
                    {level.toFixed(1)}
                  </text>
                </g>
              )
            })}
          </svg>
        </div>
        <div className="mt-1 text-xs text-gray-600">
          Lower values (blue) represent better solutions • Based on neural
          network hyperparameter tuning experiments
        </div>
      </div>
    )
  }

  return (
    <div className="my-8">
      <div className="mb-4 text-center">
        <h4 className="mb-2 text-lg font-semibold">
          Optimization Methods Comparison
        </h4>
        <p className="text-muted-foreground mx-auto max-w-2xl text-sm">
          Compare how different optimization methods explore the parameter
          space. Notice how Bayesian Optimization efficiently converges to the
          optimum with fewer evaluations.
        </p>

        {/* Add colorbar */}
        <Colorbar />
      </div>

      <div className="grid min-h-[650px] grid-cols-1 gap-6 md:grid-cols-2">
        <OptimizationMethodCard {...gridSearchData} />
        <OptimizationMethodCard {...ofatData} />
        <OptimizationMethodCard {...doeData} />
        <OptimizationMethodCard {...bayesianOptData} />
      </div>

      <div className="mt-6 rounded-md border border-blue-100 bg-blue-50 p-4">
        <h5 className="mb-2 text-sm font-semibold text-blue-800">
          Key Takeaway
        </h5>
        <p className="text-sm text-blue-700">
          Bayesian Optimization requires significantly fewer function
          evaluations (8-12 vs 25+ for other methods) to find the global
          optimum, making it ideal for expensive-to-evaluate functions. It
          adaptively learns from previous evaluations to make informed decisions
          about where to sample next.
        </p>
      </div>

      <div className="text-muted-foreground mt-4 text-center text-xs">
        <p>Data patterns adapted from research studies:</p>
        <p>
          Snoek, J., Larochelle, H., & Adams, R. P. (2012). "Practical Bayesian
          Optimization of Machine Learning Algorithms"
        </p>
        <p>
          Bergstra, J., & Bengio, Y. (2012). "Random Search for Hyper-Parameter
          Optimization"
        </p>
      </div>
    </div>
  )
}
